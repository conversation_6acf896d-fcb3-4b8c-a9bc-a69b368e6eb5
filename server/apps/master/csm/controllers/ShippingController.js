/**
 * 发货控制器
 * 负责处理发货相关的请求和响应
 * 注意：所有接口都不需要token验证，可以公开访问
 */
const BaseController = require("../../../../core/controllers/BaseController");
const multer = require('multer');
const crypto = require('crypto');
const path = require('path');
const IntegrationFactory = require('../../system/integration/common/IntegrationFactory');
const ConfigurationService = require('../../system/configure/services/ConfigurationService');

class ShippingController extends BaseController {
  constructor(prisma) {
    super(prisma);

    // 初始化集成工厂和配置服务
    this.integrationFactory = new IntegrationFactory(new ConfigurationService(prisma));

    // 初始化文件模型
    this.fileModel = prisma.files;
  }

  /**
   * 根据订单号获取配送方式列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getDeliveryMethodsByOrderNo(req, res) {
    try {
      const { orderNo } = req.params;
      const { forceMatch } = req.query; // 是否强制匹配渠道

      if (!orderNo) {
        return this.fail(res, '订单号不能为空', null, 400);
      }

      // 检查Prisma实例是否存在
      if (!this.prisma) {
        console.error('Prisma实例不存在');
        return this.fail(res, '数据库连接错误', null, 500);
      }

      // 转换强制匹配参数为布尔值
      const isForceMatch = forceMatch === 'true' || forceMatch === true;

      console.log('请求参数:', { orderNo, forceMatch, isForceMatch });

      // 首先检查配送方式表是否有数据
      try {
        const deliveryMethodCount = await this.prisma.csm_delivery_method.count();
        console.log('配送方式表记录数:', deliveryMethodCount);

        if (deliveryMethodCount === 0) {
          // 如果没有数据，创建一些基础的配送方式
          console.log('配送方式表为空，创建基础数据...');
          await this.createBasicDeliveryMethods();
        }
      } catch (error) {
        console.error('检查配送方式表失败:', error);
        return this.fail(res, `配送方式表访问失败: ${error.message}`, null, 500);
      }

      // 1. 根据订单号查找订单信息，获取渠道ID
      let channelId = null;

      console.log('查找订单渠道信息...');
      try {
        // 首先尝试从采购订单表查找
        const purchaseOrder = await this.prisma.purchase_order.findFirst({
          where: {
            OR: [
              { purchase_order_number: orderNo },
              { original_order_number: orderNo }
            ]
          },
          select: {
            channel_id: true,
            channel_name: true
          }
        });

        if (purchaseOrder && purchaseOrder.channel_id) {
          channelId = purchaseOrder.channel_id;
          console.log('从采购订单找到渠道ID:', channelId);
        }
      } catch (error) {
        console.log('采购订单查询失败:', error.message);
      }

      if (!channelId) {
        try {
          // 如果采购订单表没找到，尝试从原始订单表查找
          const originalOrder = await this.prisma.orders.findFirst({
            where: {
              OR: [
                { id: isNaN(orderNo) ? undefined : BigInt(orderNo) },
                { third_party_order_sn: orderNo }
              ]
            },
            select: {
              channel_id: true
            }
          });

          if (originalOrder && originalOrder.channel_id) {
            channelId = originalOrder.channel_id;
            console.log('从原始订单找到渠道ID:', channelId);
          }
        } catch (error) {
          console.log('原始订单查询失败:', error.message);
        }
      }

      // 如果没有找到渠道ID，使用默认值或继续处理
      if (!channelId) {
        console.log('未找到订单对应的渠道信息，使用通用配送方式');
        channelId = null; // 使用通用配送方式
      }

      // 2. 根据渠道ID和强制匹配模式查询对应的配送方式
      console.log('查询配送方式，渠道ID:', channelId, '强制匹配:', isForceMatch);

      let deliveryMethods = [];
      try {
        let whereCondition;

        if (isForceMatch && channelId) {
          // 强制匹配模式：只返回渠道ID完全匹配的配送方式
          whereCondition = {
            channel_id: channelId,
            is_enabled: 1
          };
        } else {
          // 默认模式：返回渠道匹配的和通用的配送方式
          const conditions = [{ channel_id: null }]; // 通用配送方式
          if (channelId) {
            conditions.push({ channel_id: channelId }); // 渠道专用配送方式
          }

          whereCondition = {
            OR: conditions,
            is_enabled: 1 // 只查询启用的配送方式
          };
        }

        deliveryMethods = await this.prisma.csm_delivery_method.findMany({
          where: whereCondition,
          select: {
            id: true,
            method_name: true,
            method_code: true,
            description: true,
            channel_id: true
          },
          orderBy: [
            { channel_id: 'desc' }, // 渠道专用的排在前面
            { id: 'asc' }
          ]
        });

        console.log('查询到配送方式数量:', deliveryMethods.length);
      } catch (error) {
        console.error('配送方式查询失败:', error);

        // 如果是表不存在的错误
        if (error.message.includes('does not exist') ||
            error.message.includes('relation') ||
            error.message.includes('table')) {
          return this.fail(res, '配送方式表不存在，请先创建数据库表', null, 500);
        }

        // 其他数据库错误
        return this.fail(res, `配送方式查询失败: ${error.message}`, null, 500);
      }

      // 3. 格式化返回数据
      if (deliveryMethods.length === 0) {
        return this.fail(res, '没有找到可用的配送方式，请检查数据库配置', null, 404);
      }

      const formattedMethods = deliveryMethods.map(method => ({
        id: method.id,
        methodName: method.method_name,
        methodCode: method.method_code,
        description: method.description,
        isChannelSpecific: method.channel_id !== null
      }));

      // 4. 返回标准格式响应
      this.success(res, {
        orderNo: orderNo,
        channelId: channelId ? channelId.toString() : null,
        forceMatch: isForceMatch,
        deliveryMethods: formattedMethods
      }, '获取配送方式列表成功');

    } catch (error) {
      console.error('获取配送方式列表失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, null, code);
    }
  }

  /**
   * 创建发货信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createShippingInfo(req, res) {
    try {
      const {
        orderNo,
        orderType = 1, // 默认为采购订单
        deliveryMethodId,
        expressCompanyName,
        expressCompanyCode,
        expressCompanyId,
        trackingNo,
        shippingLocation,
        attachments,
        deliveryContact,
        deliveryPhone,
        shunfengPhone,
        remarks,
        // 拆分商品发货相关字段
        isSplitOrderShipping = false,
        splitOrderNumber,
        originalOrderNumber,
        purchaseOrderNumber
      } = req.body;

      console.log('收到发货信息创建请求:', {
        orderNo,
        isSplitOrderShipping,
        splitOrderNumber,
        originalOrderNumber,
        purchaseOrderNumber
      });

      // 基础参数验证
      if (!orderNo) {
        return this.fail(res, '订单号不能为空', null, 400);
      }
      if (!deliveryMethodId) {
        return this.fail(res, '配送方式不能为空', null, 400);
      }
      if (!shippingLocation) {
        return this.fail(res, '发货地址不能为空', null, 400);
      }

      // 检查配送方式是否存在
      const deliveryMethod = await this.prisma.csm_delivery_method.findUnique({
        where: { id: parseInt(deliveryMethodId) }
      });

      if (!deliveryMethod) {
        return this.fail(res, '配送方式不存在', null, 400);
      }

      // 使用事务处理发货信息创建和订单状态更新
      const result = await this.prisma.$transaction(async (prisma) => {
        // 1. 判断是否为拆分商品发货
        if (isSplitOrderShipping) {
          console.log('处理拆分商品发货...');

          // 验证拆分订单信息
          if (!splitOrderNumber || !purchaseOrderNumber) {
            throw new Error('拆分商品发货必须提供拆分订单号和采购订单号');
          }

          // 查找拆分订单
          const splitOrder = await prisma.purchase_order_split.findFirst({
            where: {
              split_number: splitOrderNumber,
              deleted_at: null
            }
          });

          if (!splitOrder) {
            throw new Error('拆分订单不存在');
          }

          // 验证拆分单表中original_order_number是否和前端传递的采购订单编号一致
          if (splitOrder.original_order_number !== purchaseOrderNumber) {
            throw new Error('拆分订单的原始订单号与采购订单号不一致');
          }

          // 检查该拆分商品是否已经存在发货记录
          const existingShipping = await prisma.csm_shipping_info.findFirst({
            where: {
              order_no: splitOrderNumber,
              delete_time: null
            }
          });

          if (existingShipping) {
            throw new Error('该拆分订单已存在发货记录');
          }

          // 创建拆分订单发货信息
          const shippingInfo = await this.createShippingRecord(prisma, {
            orderNo: splitOrderNumber, // 使用拆分订单号作为发货记录的订单号
            orderType: 2, // 拆分商品发货类型
            deliveryMethodId,
            expressCompanyName,
            expressCompanyCode,
            expressCompanyId,
            trackingNo,
            shippingLocation,
            attachments,
            deliveryContact,
            deliveryPhone,
            shunfengPhone,
            remarks
          });

          // 检查对应采购订单下所有商品是否已经全部完成发货
          const allShippingComplete = await this.checkAllProductsShipped(prisma, purchaseOrderNumber);

          if (allShippingComplete) {
            console.log('采购订单下所有商品已完成发货，更新订单状态...');

            // 更新采购订单状态为已发货
            await this.updatePurchaseOrderStatus(prisma, purchaseOrderNumber);

            // 更新原订单状态为已发货
            await this.updateOriginalOrderStatus(prisma, splitOrder.original_order_number);
          }

          return shippingInfo;

        } else {
          console.log('处理普通发货...');

          // 普通发货：直接创建发货信息
          const shippingInfo = await this.createShippingRecord(prisma, {
            orderNo,
            orderType,
            deliveryMethodId,
            expressCompanyName,
            expressCompanyCode,
            expressCompanyId,
            trackingNo,
            shippingLocation,
            attachments,
            deliveryContact,
            deliveryPhone,
            shunfengPhone,
            remarks
          });

          // 直接更新采购订单和原订单状态为已发货
          await this.updatePurchaseOrderStatus(prisma, orderNo);

          // 查找原订单号并更新状态
          const purchaseOrder = await prisma.purchase_order.findFirst({
            where: {
              purchase_order_number: orderNo,
              deleted_at: null
            }
          });

          if (purchaseOrder && purchaseOrder.original_order_number) {
            await this.updateOriginalOrderStatus(prisma, purchaseOrder.original_order_number);
          }

          return shippingInfo;
        }
      });

      // 格式化返回数据
      const formattedShippingInfo = {
        id: result.id.toString(),
        orderNo: result.order_no,
        orderType: result.order_type,
        deliveryMethod: {
          id: result.delivery_method_id,
          name: result.delivery_method.method_name,
          code: result.delivery_method.method_code
        },
        expressCompanyName: result.express_company_name,
        expressCompanyId: result.express_company_id,
        trackingNo: result.tracking_no,
        shippingLocation: result.shipping_location,
        attachment: result.attachment,
        businessContact: result.business_contact,
        businessPhone: result.business_phone,
        remarks: result.remarks,
        createTime: result.create_time,
        updateTime: result.update_time,
        isSplitOrderShipping
      };

      console.log('发货信息创建成功:', formattedShippingInfo.id);
      this.success(res, formattedShippingInfo, '创建发货信息成功');

    } catch (error) {
      console.error('创建发货信息失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, null, code);
    }
  }

  /**
   * 创建发货记录
   * @private
   * @param {Object} prisma - Prisma事务客户端
   * @param {Object} shippingData - 发货数据
   * @returns {Object} 创建的发货信息
   */
  async createShippingRecord(prisma, shippingData) {
    const {
      orderNo,
      orderType,
      deliveryMethodId,
      expressCompanyName,
      expressCompanyCode,
      expressCompanyId,
      trackingNo,
      shippingLocation,
      attachments,
      deliveryContact,
      deliveryPhone,
      shunfengPhone,
      remarks
    } = shippingData;

    // 构建发货数据
    const shippingRecord = {
      order_no: orderNo,
      order_type: parseInt(orderType),
      delivery_method_id: parseInt(deliveryMethodId),
      shipping_location: shippingLocation,
      attachment: attachments || null,
      remarks: remarks || null
    };

    // 根据配送方式添加不同字段
    if (expressCompanyName && trackingNo) {
      // 快递配送
      shippingRecord.express_company_name = expressCompanyName;
      shippingRecord.express_company_id = expressCompanyCode || expressCompanyId || 'OTHER';
      shippingRecord.tracking_no = trackingNo;
      shippingRecord.business_contact = expressCompanyName;
      shippingRecord.business_phone = shunfengPhone || '************';
    } else if (deliveryContact && deliveryPhone) {
      // 商家配送
      shippingRecord.express_company_name = '商家配送';
      shippingRecord.express_company_id = 'MERCHANT';
      shippingRecord.tracking_no = orderNo; // 使用订单号作为跟踪号
      shippingRecord.business_contact = deliveryContact;
      shippingRecord.business_phone = deliveryPhone;
    } else {
      // 默认处理
      shippingRecord.express_company_name = '其他';
      shippingRecord.express_company_id = 'OTHER';
      shippingRecord.tracking_no = orderNo;
      shippingRecord.business_contact = '系统';
      shippingRecord.business_phone = '************';
    }

    return await prisma.csm_shipping_info.create({
      data: shippingRecord,
      include: {
        delivery_method: {
          select: {
            method_name: true,
            method_code: true
          }
        }
      }
    });
  }

  /**
   * 检查采购订单下所有商品是否已经全部完成发货
   * @private
   * @param {Object} prisma - Prisma事务客户端
   * @param {string} purchaseOrderNumber - 采购订单号
   * @returns {boolean} 是否全部完成发货
   */
  async checkAllProductsShipped(prisma, purchaseOrderNumber) {
    try {
      // 查找采购订单
      const purchaseOrder = await prisma.purchase_order.findFirst({
        where: {
          purchase_order_number: purchaseOrderNumber,
          deleted_at: null
        }
      });

      if (!purchaseOrder) {
        console.log('采购订单不存在:', purchaseOrderNumber);
        return false;
      }

      // 查找该采购订单的所有拆分订单
      const splitOrders = await prisma.purchase_order_split.findMany({
        where: {
          original_order_number: purchaseOrderNumber,
          deleted_at: null
        },
        select: {
          split_number: true
        }
      });

      console.log('找到拆分订单数量:', splitOrders.length);

      if (splitOrders.length === 0) {
        // 没有拆分订单，检查原订单是否已发货
        const originalShipping = await prisma.csm_shipping_info.findFirst({
          where: {
            order_no: purchaseOrderNumber,
            delete_time: null
          }
        });
        return !!originalShipping;
      }

      // 检查所有拆分订单是否都已发货
      for (const splitOrder of splitOrders) {
        const shippingInfo = await prisma.csm_shipping_info.findFirst({
          where: {
            order_no: splitOrder.split_number,
            delete_time: null
          }
        });

        if (!shippingInfo) {
          console.log('拆分订单未发货:', splitOrder.split_number);
          return false;
        }
      }

      console.log('所有拆分订单已完成发货');
      return true;

    } catch (error) {
      console.error('检查发货状态失败:', error);
      return false;
    }
  }

  /**
   * 更新采购订单状态为已发货
   * @private
   * @param {Object} prisma - Prisma事务客户端
   * @param {string} purchaseOrderNumber - 采购订单号
   */
  async updatePurchaseOrderStatus(prisma, purchaseOrderNumber) {
    try {
      await prisma.purchase_order.updateMany({
        where: {
          purchase_order_number: purchaseOrderNumber,
          deleted_at: null
        },
        data: {
          order_status: 2, // 待收货 (根据采购订单状态定义：0-待发货 1-发货未完成 2-待收货)
          shipping_status: 1, // 已发货 (根据发货状态定义：0-未发货 1-已发货)
          updated_at: new Date()
        }
      });

      console.log('采购订单状态更新成功:', purchaseOrderNumber);
    } catch (error) {
      console.error('更新采购订单状态失败:', error);
      throw error;
    }
  }

  /**
   * 更新原订单状态为已发货
   * @private
   * @param {Object} prisma - Prisma事务客户端
   * @param {string} originalOrderNumber - 原订单号
   */
  async updateOriginalOrderStatus(prisma, originalOrderNumber) {
    try {
      // 尝试通过订单号查找原订单
      const originalOrder = await prisma.orders.findFirst({
        where: {
          OR: [
            { third_party_order_sn: originalOrderNumber },
            { id: isNaN(originalOrderNumber) ? undefined : BigInt(originalOrderNumber) }
          ]
        }
      });

      if (originalOrder) {
        await prisma.orders.update({
          where: { id: originalOrder.id },
          data: {
            order_status: 2, // 已发货待收货 (根据原订单状态定义：0-待付款 1-已付款待发货 2-已发货待收货)
            shipping_status: 1, // 已发货 (根据发货状态定义：0-未发货 1-已发货)
            shipped_at: BigInt(Date.now()), // 设置发货时间
            updated_at: BigInt(Date.now())
          }
        });

        console.log('原订单状态更新成功:', originalOrderNumber);
      } else {
        console.log('未找到原订单:', originalOrderNumber);
      }
    } catch (error) {
      console.error('更新原订单状态失败:', error);
      throw error;
    }
  }

  /**
   * 创建基础配送方式数据
   * @private
   */
  async createBasicDeliveryMethods() {
    try {
      const basicMethods = [
        {
          method_name: '快递配送',
          method_code: 'EXPRESS',
          description: '快递配送服务',
          channel_id: null,
          is_enabled: 1
        },
        {
          method_name: '商家配送',
          method_code: 'MERCHANT',
          description: '商家自行配送',
          channel_id: null,
          is_enabled: 1
        },
        {
          method_name: '其他',
          method_code: 'OTHER',
          description: '其他配送方式',
          channel_id: null,
          is_enabled: 1
        }
      ];

      // 批量创建配送方式
      await this.prisma.csm_delivery_method.createMany({
        data: basicMethods,
        skipDuplicates: true
      });

      console.log('基础配送方式创建成功');
    } catch (error) {
      console.error('创建基础配送方式失败:', error);
      throw error;
    }
  }

  /**
   * 处理文件名编码问题
   * @private
   * @param {string} filename 原始文件名
   * @returns {string} 处理后的文件名
   */
  fixFilenameEncoding(filename) {
    if (!filename) return filename;

    try {
      // 检查是否包含乱码字符
      if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/.test(filename)) {
        // 尝试从latin1转换为utf8
        try {
          const buffer = Buffer.from(filename, 'latin1');
          const utf8Name = buffer.toString('utf8');

          // 检查转换后是否包含中文字符
          if (/[\u4e00-\u9fa5]/.test(utf8Name)) {
            return utf8Name;
          }
        } catch (e) {
          // 转换失败，继续尝试其他方法
        }

        // 尝试其他编码方式
        try {
          const bytes = [];
          for (let i = 0; i < filename.length; i++) {
            bytes.push(filename.charCodeAt(i) & 0xFF);
          }
          const buffer = Buffer.from(bytes);
          const utf8Name = buffer.toString('utf8');

          if (/[\u4e00-\u9fa5]/.test(utf8Name)) {
            return utf8Name;
          }
        } catch (e) {
          // 转换失败
        }
      }

      // 如果文件名看起来像URL编码，尝试解码
      if (filename.includes('%')) {
        try {
          const decoded = decodeURIComponent(filename);
          if (decoded !== filename) {
            return decoded;
          }
        } catch (e) {
          // 解码失败，继续使用原文件名
        }
      }

      return filename;
    } catch (error) {
      console.log('文件名编码处理失败:', error);
      return filename;
    }
  }

  /**
   * 上传文件接口（不需要鉴权）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async uploadFile(req, res) {
    try {
      console.log('发货模块 - 开始处理文件上传');

      // 检查是否有文件上传
      if (!req.file) {
        return this.fail(res, '未检测到上传文件', null, 400);
      }

      const file = req.file;
      const {
        dir = 'shipping', // 默认存储目录为shipping
        module = 'csm',   // 业务模块
        bizType = 'shipping_attachment', // 业务类型
        bizId = null,     // 业务ID
        isPublic = true   // 是否公开访问，发货附件默认公开
      } = req.body;

      console.log('发货模块 - 上传参数:', {
        dir, module, bizType, bizId, isPublic
      });

      // 处理文件名编码问题
      let originalName = file.originalname;
      if (originalName) {
        const fixedName = this.fixFilenameEncoding(originalName);
        if (fixedName !== originalName) {
          console.log('发货模块 - 文件名重新编码:', originalName, '->', fixedName);
          originalName = fixedName;
        }
      }

      console.log('发货模块 - 上传文件信息:', {
        原始文件名: file.originalname,
        处理后文件名: originalName,
        大小: file.size,
        类型: file.mimetype
      });

      // 生成文件MD5
      const md5 = crypto.createHash('md5').update(file.buffer).digest('hex');

      // 生成唯一文件名
      const extname = path.extname(originalName);
      const fileName = `${md5}${extname}`;

      console.log('发货模块 - 生成的文件名:', fileName);

      // 获取默认上传服务
      const uploadService = await this.integrationFactory.getDefaultService('Upload');
      console.log('发货模块 - 获取到上传服务类型:', uploadService.getStorageType());

      // 上传文件
      const uploadResult = await uploadService.uploadBuffer(
        file.buffer,
        fileName,
        dir,
        { contentType: file.mimetype }
      );

      console.log('发货模块 - 文件上传成功:', {
        filePath: uploadResult.filePath,
        fileUrl: uploadResult.fileUrl
      });

      // 保存文件记录
      const now = BigInt(Date.now());
      const id = BigInt(Date.now() + Math.floor(Math.random() * 1000));
      const bizIdValue = bizId ? BigInt(bizId) : null;

      const fileRecord = await this.fileModel.create({
        data: {
          id: id,
          file_name: fileName,
          original_name: originalName,
          file_path: uploadResult.filePath,
          file_url: uploadResult.fileUrl,
          file_size: BigInt(file.size),
          file_type: file.mimetype,
          extension: extname.replace('.', ''),
          storage_type: uploadService.getStorageType(),
          bucket: uploadResult.bucket || '',
          md5: md5,
          module: module,
          biz_type: bizType,
          biz_id: bizIdValue,
          is_public: isPublic === 1 || isPublic === true || isPublic === 'true' ? true : false,
          status: 1,
          created_by: null, // 不需要鉴权，用户ID为null
          created_at: now,
          updated_by: null,
          updated_at: now
        }
      });

      console.log('发货模块 - 文件记录保存成功, ID:', fileRecord.id.toString());

      return this.success(res, {
        id: fileRecord.id.toString(),
        fileName: fileRecord.file_name,
        originalName: fileRecord.original_name,
        filePath: fileRecord.file_path,
        fileUrl: fileRecord.file_url,
        fileSize: fileRecord.file_size.toString(),
        fileType: fileRecord.file_type,
        extension: fileRecord.extension,
        storageType: fileRecord.storage_type,
        md5: fileRecord.md5
      }, '文件上传成功');

    } catch (error) {
      console.error('发货模块 - 文件上传失败:', error);
      return this.fail(res, '文件上传失败: ' + error.message);
    }
  }
}

module.exports = ShippingController;
