/**
 * 发货路由
 * 处理发货相关的路由配置
 * 注意：所有发货相关接口都不需要token验证，可以公开访问
 */
const express = require('express');
const ShippingController = require('../controllers/ShippingController');

/**
 * 创建发货路由
 * @param {Object} prisma - Prisma 客户端实例
 * @returns {Object} Express 路由实例
 */
function createShippingRouter(prisma) {
  const router = express.Router();

  // 创建控制器实例
  const controller = new ShippingController(prisma);

  /**
   * 发货相关路由（公开访问，无需认证）
   */

  // 根据订单号获取配送方式列表
  router.get('/delivery-methods/:orderNo', (req, res) => {
    controller.getDeliveryMethodsByOrderNo(req, res);
  });

  // 创建发货信息
  router.post('/shipping-info', (req, res) => {
    controller.createShippingInfo(req, res);
  });

  return router;
}

module.exports = createShippingRouter;
